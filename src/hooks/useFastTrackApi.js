import { useState, useCallback } from "react";
import { trackCustomEvent } from "../utils/analytics";
import { API_ENDPOINT } from "../utils/consts";
import { customCookieStorage } from "../utils/customCookieStorage";
import { logger } from "../utils/logger";

// API status enum
const STATUS = {
  IDLE: "idle",
  LOADING: "loading",
  SUCCESS: "success",
  ERROR: "error",
};

/**
 * Custom hook to manage the fast track API call
 * @returns {Object} API state and methods
 */
export function useFastTrackApi() {
  const [status, setStatus] = useState(STATUS.IDLE);
  const [error, setError] = useState({ message: null, id: null });
  const [result, setResult] = useState(null);

  // Derived state
  const isLoading = status === STATUS.LOADING;
  const isSuccess = status === STATUS.SUCCESS;
  const isError = status === STATUS.ERROR;
  const isIdle = status === STATUS.IDLE;

  /**
   * Submit fast track application
   * @param {Object} formData - Form data to submit
   * @returns {Promise<Object>} API response
   */
  const submitFastTrack = useCallback(async (formData) => {
    let result_status = "unknown";
    let error_message = "";
    setStatus(STATUS.LOADING);
    setError({ message: null, id: null });
    setResult(null);

    const startedAt = Number(sessionStorage.getItem("fasttrack_form_started_at"));
    const submittedAt = Number(sessionStorage.getItem("fasttrack_form_submitted_at"));
    const duration = Math.round((submittedAt - startedAt) / 1000) || null;

    let result = null;
    try {
      const utmParams = {
        ...customCookieStorage.collectUtmCookies(),
        utm_dur: duration.toString(),
      };

      // Prepare the request body
      const requestBody = {
        preQualifyFields: formData,
        domain: window.location.hostname,
        utm: utmParams,
      };

      // Call the API directly using fetch
      const response = await fetch(`${API_ENDPOINT}/app/fasttrack`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      result = await response.json();

      if (!response.ok) {
        throw result;
      } else {
        result_status = "success";

        // Add the form data to the response for convenience
        result.preQualifyFields = formData;

        // Set the result
        setResult(result);
        setStatus(STATUS.SUCCESS);
      }

      // Return the response
      return result;
    } catch (error) {
      result_status = "error";
      error_message =
        error?.error ||
        error?.message ||
        "There was a problem submitting your fast track application. Please try again later.";
      const id = error?.errorId || "UNKNOWN_ERROR";
      setError({ message: error_message, id });
      setStatus(STATUS.ERROR);

      // Re-throw the error for the caller to handle
      throw error;
    } finally {
      logger.log({ result_status });

      const { businessName, firstName, lastName, email, phone, estimatedFICO } = formData;

      const eventParams = {
        businessName,
        firstName,
        lastName,
        email,
        phone,
        estimatedFICO,
        form_duration: duration,
        assigned_agent: result?.agent?.name ?? null,
      };

      trackCustomEvent("fasttrack_form_submitted", eventParams, false);
    }
  }, []);

  /**
   * Reset the API state
   */
  const reset = useCallback(() => {
    setStatus(STATUS.IDLE);
    setError({ message: null, id: null });
    setResult(null);
  }, []);

  return {
    status,
    error: error.message,
    errorId: error.id,
    result,
    isLoading,
    isSuccess,
    isError,
    isIdle,
    submitFastTrack,
    reset,
  };
}
